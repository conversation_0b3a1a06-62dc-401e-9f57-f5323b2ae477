# 📘 彩票系统接口文档

本文档记录了彩票系统的接口信息、请求参数、响应格式等，便于开发和维护。

---

## � 系统信息

**基础域名**: `https://jk7859.jvvpbv2580513aknu.com:59789`

**测试账号**:
- 用户名: `j510255295`
- 密码: `qwer8888`

---

## 📋 接口列表

### 🏠 页面相关接口

| 接口路径 | 描述 | 备注 |
|---------|------|------|
| `/index.do` | 首页 | - |
| `/login.do` | 登录页面 | - |
| `/index/lotteryHall.do?code=1&top=1` | 购彩大厅 | - |
| `/index/lotteryHall.do?code=1&top=1&showMore=1` | 购彩大厅(显示更多) | - |
| `/userCenter/help/` | 帮助中心 | - |
| `/credit/index.do?code=` | 极速赛车 | - |
| `/offcial/index.do` | 试玩极速赛车 | - |

### 🔒 验证相关接口

| 接口路径 | 描述 | 方法 |
|---------|------|------|
| `/loginVerifycode.do?timestamp=` | 登录验证码 | GET |
| `/registerVerifycode.do?timestamp=` | 注册验证码 | GET |
| `/checkAnswer.do` | 验证答案 | POST |
| `/getQuestionList.do` | 获取问题列表 | GET |

#### `/getQuestionByMember.do` - 获取用户验证问题

**请求参数**:
```
username: aefg
password: weqewqwe
verifyCode: tuc4
```

**响应示例**:
```json
{
  "question": "验证您的名字",
  "success": true
}
```

### 📢 通知相关接口

| 接口路径 | 描述 | 状态 |
|---------|------|------|
| `/newNotices.do` | 系统公告 | - |
| `/stationReceiveMessageNews.do` | 站内消息 | 空 |
| `/index/banner.do` | 横幅广告 | 空 |
| `/common/member/notice/js/notice.js?v=10.2` | 弹窗公告 | - |

### 🎮 游戏相关接口

| 接口路径 | 描述 |
|---------|------|
| `/third/forwardAg.do` | 第三方游戏跳转 |

---

## 💰 订单相关接口

### `/userCenter/userOrder/lotOrderList.do` - 获取订单列表

**请求参数**:
```
startTime: 2025-06-02 00:00:00    # 开始时间
endTime: 2025-06-08 23:59:59      # 结束时间
username:                         # 用户名(可选)
lotId:                           # 彩种ID(可选)
playCode:                        # 玩法代码(可选)
status:                          # 订单状态(可选)
pageNumber: 1                    # 页码
pageSize: 10                     # 每页数量
include: false                   # 是否包含
queryType: 1                     # 查询类型
needRefresh: false               # 是否需要刷新
sortName:                        # 排序字段(可选)
sortOrder:                       # 排序方式(可选)
```

**响应示例**:
```json
{
  "total": 704,                    // 订单总数
  "aggsData": {
    "bettingMoneyCount": 8737,     // 投注总金额
    "winMoneyCount": 9066.222,     // 奖金总额
    "kickbackCount": 0             // 总反水点数
  },
  "rows": [                        // 订单列表
    {
      "accountId": 4885250,                                    // 用户ID
      "buyIp": "*************",                               // 投注IP
      "buyMoney": 5,                                          // 投注金额
      "buyOdds": "19.800000",                                 // 投注赔率
      "buyZhuShu": 5,                                         // 投注注数
      "canUndo": false,                                       // 是否可撤单
      "cheat": false,                                         // 是否作弊
      "closedTime": "2025-06-03 13:45:18",                   // 订单关闭时间
      "createTime": "2025-06-03 13:44:32",                   // 投注时间
      "currentRebate": 4,                                     // 当前返水率
      "haoMa": "02|03|05|06|08",                             // 投注号码
      "id": *********,                                        // 订单ID
      "kickback": 0,                                          // 反水点数
      "lotCode": "JSSC168",                                   // 游戏代码
      "lotName": "168极速赛车[官]",                            // 游戏名称
      "lotType": 3,                                           // 游戏类型
      "lotVersion": 1,                                        // 游戏版本
      "lunarYear": 0,                                         // 农历年
      "model": 100,                                           // 模式
      "multiple": 50,                                         // 倍数
      "oddsCode": "dwd_1",                                    // 赔率代码
      "openHaoMa": "08,05,06,04,10,09,01,02,07,03",          // 开奖号码
      "openTime": "2025-06-03 13:45:37",                     // 开奖时间
      "orderId": "C250603192051",                             // 订单编号
      "playCode": "dwd2",                                     // 玩法代码
      "playName": "定位胆[冠军]",                              // 玩法名称
      "proxyRollback": 2,                                     // 代理返水
      "qiHao": "33614949",                                    // 期号
      "rollBackStatus": 2,                                    // 返水状态
      "sellingTime": "2025-06-03 13:44:18",                  // 销售时间
      "signKey": "0aa0056b037acb4a1cbe1c92c2d489f5",          // 签名密钥
      "stationId": 6,                                         // 站点ID
      "status": 2,                                            // 订单状态 (1:未开奖, 2:已中奖, 3:未中奖, 4:已撤单, 5:派奖回滚成功, 6:和局)
      "terminalBetType": 2,                                   // 终端投注类型
      "updateTime": "2025-06-03 13:46:15",                   // 更新时间
      "username": "j510255295",                               // 用户名
      "winMoney": 9.9,                                        // 中奖金额
      "winOdds": "19.800000",                                 // 中奖赔率
      "winZhuShu": 1,                                         // 中奖注数
      "zhuiHao": 1                                            // 追号标识
    }
    // ... 更多订单数据
  ]
}
```

---

## 🎯 投注相关接口

### `/offcial/doBet.do` - 投注请求

**请求参数**:
```
code: JSSC168                                    # 游戏代码
qiHao: ********                                  # 期号
orderList: [{"i":"dwd_1","c":"04","n":1,"t":1,"k":0,"m":1,"a":2}]  # 订单列表
```

**URL编码后的参数**:
```
code: JSSC168
qiHao: ********
orderList: %5B%7B%22i%22%3A%22dwd_1%22%2C%22c%22%3A%2204%22%2C%22n%22%3A1%2C%22t%22%3A1%2C%22k%22%3A0%2C%22m%22%3A1%2C%22a%22%3A2%7D%5D
```

**响应示例**:
```json
{
  "msg": "下注成功",
  "insertedOrder": [
    {
      "accountId": 5071083,                       // 账户ID
      "buyIp": "*************",                  // 投注IP
      "buyMoney": 2,                             // 投注金额
      "buyOdds": "19.800000",                    // 投注赔率
      "buyZhuShu": 1,                            // 投注注数
      "canUndo": false,                          // 是否可撤单
      "cheat": false,                            // 是否作弊
      "closedTime": "2025-06-04 21:09:03",      // 关闭时间
      "createTime": "2025-06-04 21:08:15",      // 创建时间
      "currentRebate": 7.5,                     // 当前返水率
      "haoMa": "04",                             // 投注号码
      "kickback": 0,                             // 反水点数
      "lotCode": "JSSC168",                      // 游戏代码
      "lotType": 3,                              // 游戏类型
      "lotVersion": 1,                           // 游戏版本
      "lunarYear": 0,                            // 农历年
      "model": 1,                                // 模式
      "multiple": 1,                             // 倍数
      "oddsCode": "dwd_1",                       // 赔率代码
      "orderId": "C250604324447",                // 订单编号
      "periodStatus": 0,                         // 期状态
      "playCode": "dwd2",                        // 玩法代码
      "playName": "定位胆[冠军]",                 // 玩法名称
      "proxyRollback": 1,                        // 代理返水
      "qiHao": "********",                       // 期号
      "rollBackStatus": 1,                       // 返水状态
      "sellingTime": "2025-06-04 21:08:03",     // 销售时间
      "signKey": "e0a27a35928cf9449eea6b0c503ac057", // 签名密钥
      "stationId": 6,                            // 站点ID
      "status": 1,                               // 订单状态
      "terminalBetType": 1,                      // 终端投注类型
      "username": "guest12183",                  // 用户名
      "winMoney": 0,                             // 中奖金额
      "zhuiHao": 1                               // 追号标识
    }
  ],
  "success": true
}
```

---

## 👤 账户相关接口

### `/accInfo/getAccount.do` - 获取账户信息

**请求参数**:
```
_: *************    # 时间戳参数
```

**响应示例**:
```json
{
  "accountId": 5071083,        // 账户ID
  "email": "",                 // 邮箱
  "money": 1998,               // 账户余额
  "phone": "",                 // 手机号
  "qq": "",                    // QQ号
  "realName": "",              // 真实姓名
  "score": -1,                 // 积分
  "stationId": 6,              // 站点ID
  "username": "guest12183",    // 用户名
  "wechat": ""                 // 微信号
}
```

https://jk7859.jvvpbv2580513aknu.com:59789/lotData/getLotteryResultList.do?code=JSSC168&pageSize=30&pageIndex=1&_=************* #历史开奖记录
返回preview:
{
  "total": 8064,
  "list": [
    {
      "result": "09,07,05,03,02,10,01,06,08,04",
      "date": "2025-06-04 23:03:03",
      "period": "********",
      "id": ********
    },
    {
      "result": "05,06,10,02,01,08,04,03,09,07",
      "date": "2025-06-04 23:01:48",
      "period": "********",
      "id": ********
    },
    {
      "result": "05,07,03,02,08,04,10,09,01,06",
      "date": "2025-06-04 23:00:33",
      "period": "33616545",
      "id": 12454560
    },
    {
      "result": "06,02,05,10,09,04,08,07,03,01",
      "date": "2025-06-04 22:59:18",
      "period": "33616544",
      "id": 12454559
    },
    {
      "result": "01,04,07,09,03,08,05,02,06,10",
      "date": "2025-06-04 22:58:03",
      "period": "33616543",
      "id": 12454558
    },
    {
      "result": "06,01,03,02,04,10,07,08,09,05",
      "date": "2025-06-04 22:56:48",
      "period": "33616542",
      "id": 12454557
    },
    {
      "result": "02,08,06,09,05,03,01,10,07,04",
      "date": "2025-06-04 22:55:33",
      "period": "33616541",
      "id": 12454556
    },
    {
      "result": "05,01,03,07,10,09,08,06,04,02",
      "date": "2025-06-04 22:54:18",
      "period": "33616540",
      "id": 12454555
    },
    {
      "result": "07,01,04,09,10,03,02,05,08,06",
      "date": "2025-06-04 22:53:03",
      "period": "33616539",
      "id": 12454554
    },
    {
      "result": "02,06,10,09,03,04,05,08,07,01",
      "date": "2025-06-04 22:51:48",
      "period": "33616538",
      "id": 12454553
    },
    {
      "result": "06,07,04,10,09,05,02,01,03,08",
      "date": "2025-06-04 22:50:33",
      "period": "33616537",
      "id": 12454552
    },
    {
      "result": "09,07,06,04,08,01,05,02,10,03",
      "date": "2025-06-04 22:49:18",
      "period": "33616536",
      "id": 12454551
    },
    {
      "result": "05,02,01,06,08,04,07,09,10,03",
      "date": "2025-06-04 22:48:03",
      "period": "33616535",
      "id": 12454550
    },
    {
      "result": "01,10,07,05,02,09,03,04,08,06",
      "date": "2025-06-04 22:46:48",
      "period": "33616534",
      "id": 12454549
    },
    {
      "result": "02,08,10,03,09,05,07,06,04,01",
      "date": "2025-06-04 22:45:33",
      "period": "33616533",
      "id": 12454548
    },
    {
      "result": "01,10,04,08,07,09,06,03,02,05",
      "date": "2025-06-04 22:44:18",
      "period": "33616532",
      "id": 12454547
    },
    {
      "result": "04,08,02,03,05,07,06,01,10,09",
      "date": "2025-06-04 22:43:03",
      "period": "33616531",
      "id": 12454546
    },
    {
      "result": "04,09,10,03,06,07,02,05,08,01",
      "date": "2025-06-04 22:41:48",
      "period": "33616530",
      "id": 12454545
    },
    {
      "result": "06,09,10,01,07,04,08,02,05,03",
      "date": "2025-06-04 22:40:33",
      "period": "33616529",
      "id": 12454544
    },
    {
      "result": "01,08,03,06,04,10,07,09,02,05",
      "date": "2025-06-04 22:39:18",
      "period": "33616528",
      "id": 12454543
    },
    {
      "result": "07,02,04,10,03,09,06,08,05,01",
      "date": "2025-06-04 22:38:03",
      "period": "33616527",
      "id": 12454542
    },
    {
      "result": "06,02,07,09,04,08,05,03,01,10",
      "date": "2025-06-04 22:36:48",
      "period": "33616526",
      "id": 12454541
    },
    {
      "result": "10,02,03,08,06,04,05,07,01,09",
      "date": "2025-06-04 22:35:33",
      "period": "33616525",
      "id": 12454540
    },
    {
      "result": "06,02,03,08,04,01,07,05,10,09",
      "date": "2025-06-04 22:34:18",
      "period": "33616524",
      "id": 12454539
    },
    {
      "result": "09,02,05,07,06,08,04,01,10,03",
      "date": "2025-06-04 22:33:03",
      "period": "33616523",
      "id": 12454538
    },
    {
      "result": "07,08,04,10,05,06,02,09,03,01",
      "date": "2025-06-04 22:31:48",
      "period": "33616522",
      "id": 12454537
    },
    {
      "result": "07,02,04,10,06,03,08,05,01,09",
      "date": "2025-06-04 22:30:33",
      "period": "33616521",
      "id": 12454536
    },
    {
      "result": "01,03,07,09,06,05,02,10,04,08",
      "date": "2025-06-04 22:29:18",
      "period": "********",
      "id": ********
    },
    {
      "result": "09,08,04,05,10,03,02,07,01,06",
      "date": "2025-06-04 22:28:03",
      "period": "********",
      "id": ********
    },
    {
      "result": "01,03,08,09,04,06,05,10,02,07",
      "date": "2025-06-04 22:26:48",
      "period": "********",
      "id": ********
    }
  ]
}

https://jk7859.jvvpbv2580513aknu.com:59789/accInfo/getAccount.do?_=*************
返回preview:
{
  "accountId": 4885250, #账号id
  "email": "", #邮箱
  "money": 343.35, #余额
  "phone": "150***0976", #手机
  "qq": "", #qq
  "realName": "**海", #真实姓名
  "score": 6792, #积分
  "stationId": 6,    #站点id
  "username": "j510255295", #账号
  "wechat": ""  #微信
}






```